Metadata-Version: 2.1
Name: pycache
Version: 0.1.0
Summary: A Redis-like in-memory data structure store implementation in Python
Home-page: 
Author: PyCache Team
Author-email: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.7
Description-Content-Type: text/markdown
Provides-Extra: dev
Requires-Dist: pytest>=6.0; extra == "dev"
Requires-Dist: pytest-cov; extra == "dev"
Requires-Dist: black; extra == "dev"
Requires-Dist: flake8; extra == "dev"
Requires-Dist: mypy; extra == "dev"

# PyCache

A Redis-like in-memory data structure store implementation in Python.

PyCache is a in-memory data structure store. 

- Has basic operations for SET, GET, and DELETE.
- Time based expiration
- TCP server for client connections
- Extension to make string and other data types compatible with Redis.
