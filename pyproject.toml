[build-system]
requires = ["setuptools", "setuptools-scm", "wheel"]
build-backend = "setuptools.build_meta"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"

[project]
name = "pycache"
version = "0.1.0"
description = "A Redis-like in-memory data structure store implementation in Python"
readme = "README.md"
authors = [{ name = "Vaishnavi Jadhav", email = "<EMAIL>" }]
license = { file = "LICENSE" }
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
