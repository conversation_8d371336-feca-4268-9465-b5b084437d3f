[build-system]
requires = ["setuptools>=61.0", "setuptools-scm>=6.2", "wheel>=0.37.0", "build>=0.8.0", "pylint>=2.17.5"]
build-backend = "build"

[tool.black]
line-length = 88
target-version = ['py37']
include = '\.pyi?$'
skip-magic-trailing-comma = true

[tool.isort]
profile = "black"
line_length = 88
skip_magic_trailing_comma = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
addopts = "-ra --cov=pycache --cov-report=xml --cov-report=term-missing"

[project]
name = "pycache"
version = "0.1.0"
description = "A Redis-like in-memory data structure store implementation in Python"
readme = "README.md"
authors = [{ name = "PyCache Team", email = "" }]
license = { file = "LICENSE" }
urls = { repository = "", documentation = "" }
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
requires-python = ">=3.7"
dependencies = []

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov",
    "black",
    "flake8",
    "mypy",
]
