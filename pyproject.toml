[build-system]
requires = ["setuptools>=61.0", "setuptools-scm>=6.2", "wheel>=0.37.0", "build>=0.8.0"]
build-backend = "setuptools.build_meta"

[tool.black]
line-length = 88
target-version = ['py37']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"

[project]
name = "pycache"
version = "0.1.0"
description = "A Redis-like in-memory data structure store implementation in Python"
readme = "README.md"
authors = [{ name = "PyCache Team", email = "" }]
license = { file = "LICENSE" }
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
requires-python = ">=3.7"
dependencies = []

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov",
    "black",
    "flake8",
    "mypy",
]
